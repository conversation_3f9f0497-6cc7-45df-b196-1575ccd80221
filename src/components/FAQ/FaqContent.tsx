"use client";

import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionItemButton,
  AccordionItemHeading,
  AccordionItemPanel,
} from "react-accessible-accordion";
import { faqData } from "@/api/faq";
import ContactForm from "@/components/FAQ/ContactForm";

const FaqContent = () => {
  return (
    <div className="faq-area ptb-80">
      <div className="container">
        {faqData.map((category) => (
          <div key={category.id} className="faq-category">
            <div className="faq-accordion mb-4">
              <Accordion allowZeroExpanded>
                <h3 className="faq-category-title mb-3">{category.title}</h3>
                {category.items.map((item) => (
                  <AccordionItem key={item.id} uuid={item.id}>
                    <AccordionItemHeading>
                      <AccordionItemButton>
                        <span>{item.question}</span>
                      </AccordionItemButton>
                    </AccordionItemHeading>
                    <AccordionItemPanel>
                      <p>{item.answer}</p>
                    </AccordionItemPanel>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </div>
        ))}
        <hr />
        <ContactForm />
      </div>
    </div>
  );
};

export default FaqContent;
