"use client";

import React from "react";
import Link from "next/link";
import { Autoplay } from 'swiper/modules';
import EffectWrapper from '@/components/EffectWrapper';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import Image from 'next/image';
import { assets } from "@/utils/helper";

const Technologies = () => {
  return (
    <>
      <div className="ready-to-talk">
        <div className="container">
          <h3>Ready to talk?</h3>
          <p>Our team is here to help you design, build, and launch your ideas.
            <br />
            Let&#39;s collaborate to create scalable, high-impact digital products.
          </p>

          <Link href="/contact" className="btn btn-primary">
            Contact Us
          </Link>

        </div>
      </div>

      <div className="partner-area partner-section">
        <div className="container">
          <h5>Building success stories with every project we take on.</h5>
          <div className="partner-inner partners__custom-background">
            <EffectWrapper>
              <Swiper
                modules={[Autoplay]}
                spaceBetween={16}
                loop={true}
                initialSlide={4}
                freeMode={true}
                slidesPerView={1.8}
                speed={4000}
                className="swiper-remove-time-transition"
                autoplay={{
                  delay: 0,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: false
                }}
                breakpoints={{
                  0: {
                    slidesPerView: 1.2
                  },
                  375: {
                    slidesPerView: 1.5
                  },
                  400: {
                    slidesPerView: 1.7
                  },
                  425: {
                    slidesPerView: 1.9
                  },
                  475: {
                    slidesPerView: 2.2
                  },
                  520: {
                    slidesPerView: 2.5
                  },
                  768: {
                    slidesPerView: 3.5
                  },
                  992: {
                    slidesPerView: 5
                  },
                  1200: {
                    slidesPerView: 6
                  },
                }}
              >
                <SwiperSlide key={'angular'}>
                  <div className="tech-slide angular">
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-default"
                    >
                      <path
                        d="M2 9.94476L29.9114 0L58.5745 9.76794L53.9335 46.6961L29.9114 60L6.26521 46.8729L2 9.94476Z"
                        fill="#7780A1"
                      />
                      <path
                        d="M29.9555 7.00586L12.5635 45.702L19.0606 45.5915L22.5524 36.8621H38.1545L41.9778 45.702L48.1876 45.8125L29.9555 7.00586ZM29.9999 19.4037L35.8782 31.6908H24.8284L29.9999 19.4037Z"
                        fill="white"
                      />
                    </svg>
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-hover"
                    >
                      <path
                        d="M2 9.94476L29.9114 0L58.5745 9.76794L53.9335 46.6961L29.9114 60L6.26521 46.8729L2 9.94476Z"
                        fill="white"
                      />
                      <path
                        d="M29.9555 7.00586L12.5635 45.702L19.0606 45.5915L22.5524 36.8621H38.1545L41.9778 45.702L48.1876 45.8125L29.9555 7.00586ZM29.9999 19.4037L35.8782 31.6908H24.8284L29.9999 19.4037Z"
                        fill="#DF2E31"
                      />
                    </svg>
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'next'}>
                  <div className="tech-slide next">
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-default"
                    >
                      <path
                        d="M28.3123 4.299C28.2018 4.30905 27.8499 4.34423 27.5332 4.36937C20.2297 5.02784 13.3887 8.96861 9.05585 15.0255C6.64313 18.3933 5.1 22.2134 4.51692 26.2597C4.31084 27.6722 4.28571 28.0894 4.28571 30.0045C4.28571 31.9196 4.31084 32.3368 4.51692 33.7492C5.91429 43.4051 12.7855 51.5179 22.1046 54.5237C23.7734 55.0615 25.5327 55.4285 27.5332 55.6496C28.3123 55.7351 31.6801 55.7351 32.4592 55.6496C35.9124 55.2676 38.8378 54.4131 41.723 52.9404C42.1654 52.7142 42.2508 52.6539 42.1905 52.6036C42.1503 52.5734 40.2653 50.0451 38.0034 46.989L33.8917 41.4347L28.7396 33.8095C25.9046 29.6174 23.5724 26.1894 23.5522 26.1894C23.5321 26.1843 23.512 29.5722 23.502 33.709C23.4869 40.9522 23.4819 41.2437 23.3914 41.4146C23.2607 41.6609 23.1602 41.7614 22.9491 41.872C22.7882 41.9524 22.6475 41.9675 21.8885 41.9675H21.0189L20.7877 41.8218C20.6369 41.7263 20.5263 41.6006 20.4509 41.4548L20.3453 41.2286L20.3554 31.1505L20.3705 21.0674L20.5263 20.8713C20.6067 20.7658 20.7776 20.6301 20.8983 20.5647C21.1043 20.4642 21.1848 20.4541 22.0544 20.4541C23.0798 20.4541 23.2507 20.4944 23.5171 20.7859C23.5925 20.8663 26.3822 25.0685 29.7198 30.1301C33.0573 35.1918 37.6214 42.1032 39.8632 45.4961L43.9347 51.6636L44.1408 51.5279C45.9654 50.3417 47.8956 48.6528 49.4236 46.8935C52.6757 43.1588 54.7718 38.6048 55.4755 33.7492C55.6816 32.3368 55.7067 31.9196 55.7067 30.0045C55.7067 28.0894 55.6816 27.6722 55.4755 26.2597C54.0781 16.6038 47.2069 8.49109 37.8878 5.48525C36.2441 4.95244 34.4949 4.58551 32.5346 4.36434C32.052 4.31408 28.7295 4.25878 28.3123 4.299ZM38.8378 19.851C39.0791 19.9716 39.2751 20.2028 39.3455 20.4441C39.3857 20.5748 39.3958 23.3695 39.3857 29.6677L39.3706 38.7053L37.7772 36.2625L36.1788 33.8196V27.25C36.1788 23.0026 36.1989 20.615 36.2291 20.4994C36.3095 20.2179 36.4854 19.9967 36.7267 19.866C36.9328 19.7605 37.0082 19.7504 37.7973 19.7504C38.5413 19.7504 38.6719 19.7605 38.8378 19.851Z"
                        fill="#7780A1"
                      />
                      <path
                        d="M43.6834 51.7735C43.5074 51.8841 43.4521 51.9595 43.608 51.8741C43.7185 51.8087 43.8995 51.673 43.8693 51.668C43.8543 51.668 43.7688 51.7182 43.6834 51.7735ZM43.3365 51.9997C43.2461 52.0701 43.2461 52.0751 43.3566 52.0198C43.417 51.9897 43.4672 51.9545 43.4672 51.9444C43.4672 51.9042 43.4421 51.9143 43.3365 51.9997ZM43.0852 52.1505C42.9947 52.2209 42.9947 52.2259 43.1053 52.1706C43.1656 52.1405 43.2159 52.1053 43.2159 52.0952C43.2159 52.055 43.1908 52.0651 43.0852 52.1505ZM42.8339 52.3013C42.7434 52.3717 42.7434 52.3767 42.854 52.3214C42.9143 52.2913 42.9646 52.2561 42.9646 52.246C42.9646 52.2058 42.9394 52.2159 42.8339 52.3013ZM42.4519 52.5024C42.2609 52.6029 42.2709 52.6431 42.4619 52.5476C42.5474 52.5024 42.6127 52.4571 42.6127 52.4471C42.6127 52.4119 42.6077 52.4169 42.4519 52.5024Z"
                        fill="#7780A1"
                      />
                    </svg>
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-hover"
                    >
                      <path
                        d="M28.3123 4.299C28.2018 4.30905 27.8499 4.34423 27.5332 4.36937C20.2297 5.02784 13.3887 8.96861 9.05585 15.0255C6.64313 18.3933 5.1 22.2134 4.51692 26.2597C4.31084 27.6722 4.28571 28.0894 4.28571 30.0045C4.28571 31.9196 4.31084 32.3368 4.51692 33.7492C5.91429 43.4051 12.7855 51.5179 22.1046 54.5237C23.7734 55.0615 25.5327 55.4285 27.5332 55.6496C28.3123 55.7351 31.6801 55.7351 32.4592 55.6496C35.9124 55.2676 38.8378 54.4131 41.723 52.9404C42.1654 52.7142 42.2508 52.6539 42.1905 52.6036C42.1503 52.5734 40.2653 50.0451 38.0034 46.989L33.8917 41.4347L28.7396 33.8095C25.9046 29.6174 23.5724 26.1894 23.5522 26.1894C23.5321 26.1843 23.512 29.5722 23.502 33.709C23.4869 40.9522 23.4819 41.2437 23.3914 41.4146C23.2607 41.6609 23.1602 41.7614 22.9491 41.872C22.7882 41.9524 22.6475 41.9675 21.8885 41.9675H21.0189L20.7877 41.8218C20.6369 41.7263 20.5263 41.6006 20.4509 41.4548L20.3453 41.2286L20.3554 31.1505L20.3705 21.0674L20.5263 20.8713C20.6067 20.7658 20.7776 20.6301 20.8983 20.5647C21.1043 20.4642 21.1848 20.4541 22.0544 20.4541C23.0798 20.4541 23.2507 20.4944 23.5171 20.7859C23.5925 20.8663 26.3822 25.0685 29.7198 30.1301C33.0573 35.1918 37.6214 42.1032 39.8632 45.4961L43.9347 51.6636L44.1408 51.5279C45.9654 50.3417 47.8956 48.6528 49.4236 46.8935C52.6757 43.1588 54.7718 38.6048 55.4755 33.7492C55.6816 32.3368 55.7067 31.9196 55.7067 30.0045C55.7067 28.0894 55.6816 27.6722 55.4755 26.2597C54.0781 16.6038 47.2069 8.49109 37.8878 5.48525C36.2441 4.95244 34.4949 4.58551 32.5346 4.36434C32.052 4.31408 28.7295 4.25878 28.3123 4.299ZM38.8378 19.851C39.0791 19.9716 39.2751 20.2028 39.3455 20.4441C39.3857 20.5748 39.3958 23.3695 39.3857 29.6677L39.3706 38.7053L37.7772 36.2625L36.1788 33.8196V27.25C36.1788 23.0026 36.1989 20.615 36.2291 20.4994C36.3095 20.2179 36.4854 19.9967 36.7267 19.866C36.9328 19.7605 37.0082 19.7504 37.7973 19.7504C38.5413 19.7504 38.6719 19.7605 38.8378 19.851Z"
                        fill="white"
                      />
                      <path
                        d="M43.6834 51.7735C43.5074 51.8841 43.4521 51.9595 43.608 51.8741C43.7185 51.8087 43.8995 51.673 43.8693 51.668C43.8543 51.668 43.7688 51.7182 43.6834 51.7735ZM43.3365 51.9997C43.2461 52.0701 43.2461 52.0751 43.3566 52.0198C43.417 51.9897 43.4672 51.9545 43.4672 51.9444C43.4672 51.9042 43.4421 51.9143 43.3365 51.9997ZM43.0852 52.1505C42.9947 52.2209 42.9947 52.2259 43.1053 52.1706C43.1656 52.1405 43.2159 52.1053 43.2159 52.0952C43.2159 52.055 43.1908 52.0651 43.0852 52.1505ZM42.8339 52.3013C42.7434 52.3717 42.7434 52.3767 42.854 52.3214C42.9143 52.2913 42.9646 52.2561 42.9646 52.246C42.9646 52.2058 42.9394 52.2159 42.8339 52.3013ZM42.4519 52.5024C42.2609 52.6029 42.2709 52.6431 42.4619 52.5476C42.5474 52.5024 42.6127 52.4571 42.6127 52.4471C42.6127 52.4119 42.6077 52.4169 42.4519 52.5024Z"
                        fill="white"
                      />
                    </svg>
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'vue'}>
                  <div className="tech-slide vue">
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-default"
                    >
                      <path
                        opacity="0.3"
                        d="M35.939 7.71484L30.0001 18.0006L24.0611 7.71484H4.28577L30.0001 52.254L55.7143 7.71484H35.939Z"
                        fill="#7780A1"
                      />
                      <path
                        d="M35.9391 7.71484L30.0001 18.0006L24.0611 7.71484H14.5715L30.0001 34.4371L45.4287 7.71484H35.9391Z"
                        fill="#7780A1"
                      />
                    </svg>
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-hover"
                    >
                      <path
                        d="M35.939 7.71484L30 18.0006L24.061 7.71484H4.28571L30 52.254L55.7143 7.71484H35.939Z"
                        fill="white"
                      />
                      <path
                        d="M35.939 7.71484L30 18.0006L24.061 7.71484H14.5714L30 34.4371L45.4286 7.71484H35.939Z"
                        fill="#31475E"
                      />
                    </svg>
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'php'}>
                  <div className="tech-slide php">
                    <svg
                      width="70"
                      height="60"
                      viewBox="0 0 70 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-default"
                    >
                      <path
                        d="M35 48.4383C54.33 48.4383 70 40.2813 70 30.2191C70 20.157 54.33 12 35 12C15.67 12 0 20.157 0 30.2191C0 40.2813 15.67 48.4383 35 48.4383Z"
                        fill="#7780A1"
                      />
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M9.82886 41.0066L13.6646 21.5887H22.5341C26.3696 21.8285 28.2875 23.7461 28.2875 27.1025C28.2875 32.8559 23.7329 36.2121 19.6573 35.9723H15.3427L14.3838 41.0066H9.82886ZM16.3014 32.3766L17.5001 25.1846H20.6165C22.2946 25.1846 23.4931 25.9038 23.4931 27.3421C23.2535 31.4177 21.3357 32.1368 19.1782 32.3766H16.3017H16.3014ZM27.3964 35.9723L31.2319 16.5547H35.7868L34.8279 21.5887H39.1427C42.9785 21.8285 44.4168 23.7461 43.9375 26.1436L42.2594 35.9723H37.4649L39.143 27.1025C39.3825 25.9038 39.3825 25.1846 37.7047 25.1846H34.1087L31.9513 35.9723H27.3964ZM41.9523 41.0066L45.7878 21.5887H54.6576C58.4934 21.8285 60.4112 23.7461 60.4112 27.1025C60.4112 32.8559 55.8563 36.2121 51.781 35.9723H47.4659L46.5069 41.0066H41.9523ZM48.4246 32.3766L49.6233 25.1846H52.74C54.418 25.1846 55.6165 25.9038 55.6165 27.3421C55.377 31.4177 53.4591 32.1368 51.3017 32.3766H48.4248H48.4246Z"
                        fill="white"
                      />
                    </svg>
                    <svg
                      width="70"
                      height="60"
                      viewBox="0 0 70 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-hover"
                    >
                      <path
                        d="M35 48.4383C54.33 48.4383 70 40.2813 70 30.2191C70 20.157 54.33 12 35 12C15.67 12 0 20.157 0 30.2191C0 40.2813 15.67 48.4383 35 48.4383Z"
                        fill="white"
                      />
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M9.8288 41.0066L13.6646 21.5887H22.5341C26.3696 21.8285 28.2875 23.7461 28.2875 27.1025C28.2875 32.8559 23.7328 36.2121 19.6572 35.9723H15.3427L14.3837 41.0066H9.8288ZM16.3013 32.3766L17.5001 25.1846H20.6165C22.2945 25.1846 23.493 25.9038 23.493 27.3421C23.2535 31.4177 21.3356 32.1368 19.1782 32.3766H16.3016H16.3013ZM27.3963 35.9723L31.2318 16.5547H35.7868L34.8278 21.5887H39.1427C42.9784 21.8285 44.4167 23.7461 43.9374 26.1436L42.2593 35.9723H37.4649L39.1429 27.1025C39.3825 25.9038 39.3825 25.1846 37.7047 25.1846H34.1087L31.9513 35.9723H27.3963ZM41.9522 41.0066L45.7877 21.5887H54.6575C58.4933 21.8285 60.4112 23.7461 60.4112 27.1025C60.4112 32.8559 55.8563 36.2121 51.7809 35.9723H47.4658L46.5069 41.0066H41.9522ZM48.4245 32.3766L49.6232 25.1846H52.7399C54.418 25.1846 55.6165 25.9038 55.6165 27.3421C55.3769 31.4177 53.459 32.1368 51.3016 32.3766H48.4248H48.4245Z"
                        fill="#4F5B93"
                      />
                    </svg>
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'laravel'}>
                  <div className="tech-slide laravel">
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-default"
                    >
                      <path
                        d="M59.7593 28.1947C59.3314 27.7668 53.84 20.849 52.9129 19.708C51.9145 18.5669 51.4866 18.7809 50.8447 18.8522C50.2029 18.9235 43.2852 20.1359 42.5007 20.2072C41.7162 20.3498 41.217 20.6351 41.7162 21.3483C42.1441 21.9901 46.7084 28.4086 47.7068 29.9063L29.5211 34.2566L15.1151 10.0802C14.5446 9.22444 14.4019 8.93918 13.1182 9.01049C11.8345 9.08181 1.77891 9.93761 1.06574 9.93761C0.352576 10.0089 -0.431907 10.2942 0.281259 12.0058C0.994425 13.7174 12.4051 38.2503 12.6903 38.9635C12.9756 39.6766 13.8314 40.8177 15.757 40.3898C17.7538 39.8906 24.6002 38.1077 28.38 37.1092C30.3769 40.6751 34.3706 47.9494 35.1551 49.0191C36.1535 50.4454 36.8667 50.1602 38.3643 49.7323C39.5767 49.3757 57.0493 43.0998 57.8338 42.7432C58.6182 42.3867 59.1175 42.1727 58.5469 41.3882C58.119 40.8177 53.5548 34.6132 51.13 31.4039C52.7703 30.976 58.6896 29.407 59.3314 29.1931C60.0446 28.9791 60.1872 28.6226 59.7593 28.1947ZM26.7397 34.9697C26.5258 35.0411 16.3275 37.4658 15.8283 37.6084C15.2577 37.7511 15.2577 37.6798 15.2577 37.4658C15.1151 37.2519 3.13392 12.4337 2.91997 12.1484C2.77734 11.8632 2.77734 11.5779 2.91997 11.5779C3.06261 11.5779 12.5477 10.7221 12.833 10.7221C13.1896 10.7221 13.1182 10.7934 13.2609 11.0074C13.2609 11.0074 26.5971 34.0426 26.811 34.3992C27.0963 34.7558 26.9537 34.8984 26.7397 34.9697ZM55.409 40.3185C55.5516 40.6037 55.7656 40.7464 55.195 40.889C54.6958 41.103 38.0077 46.737 37.6512 46.8796C37.2946 47.0222 37.0806 47.0936 36.6527 46.4517C36.2248 45.8099 30.8048 36.4674 30.8048 36.4674L48.5626 31.8318C48.9905 31.6892 49.1331 31.6179 49.4184 32.0458C49.7037 32.545 55.2664 40.1045 55.409 40.3185ZM56.5501 27.7668C56.1222 27.8381 49.6323 29.4784 49.6323 29.4784L44.2836 22.2041C44.141 21.9901 43.9983 21.7762 44.3549 21.7048C44.7115 21.6335 50.7734 20.5638 51.0587 20.4925C51.3439 20.4211 51.5579 20.3498 51.9145 20.849C52.2711 21.2769 56.8353 27.1249 57.0493 27.3389C57.2632 27.5528 56.978 27.6954 56.5501 27.7668Z"
                        fill="#7780A1"
                      />
                    </svg>
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-hover"
                    >
                      <path
                        d="M59.7593 28.1947C59.3314 27.7668 53.84 20.849 52.9129 19.708C51.9145 18.5669 51.4866 18.7809 50.8447 18.8522C50.2029 18.9235 43.2852 20.1359 42.5007 20.2072C41.7162 20.3498 41.217 20.6351 41.7162 21.3483C42.1441 21.9901 46.7084 28.4086 47.7068 29.9063L29.5211 34.2566L15.1151 10.0802C14.5446 9.22444 14.4019 8.93918 13.1182 9.01049C11.8345 9.08181 1.77891 9.93761 1.06574 9.93761C0.352576 10.0089 -0.431907 10.2942 0.281259 12.0058C0.994425 13.7174 12.4051 38.2503 12.6903 38.9635C12.9756 39.6766 13.8314 40.8177 15.757 40.3898C17.7538 39.8906 24.6002 38.1077 28.38 37.1092C30.3769 40.6751 34.3706 47.9494 35.1551 49.0191C36.1535 50.4454 36.8667 50.1602 38.3643 49.7323C39.5767 49.3757 57.0493 43.0998 57.8338 42.7432C58.6182 42.3867 59.1175 42.1727 58.5469 41.3882C58.119 40.8177 53.5548 34.6132 51.13 31.4039C52.7703 30.976 58.6896 29.407 59.3314 29.1931C60.0446 28.9791 60.1872 28.6226 59.7593 28.1947ZM26.7397 34.9697C26.5258 35.0411 16.3275 37.4658 15.8283 37.6084C15.2577 37.7511 15.2577 37.6798 15.2577 37.4658C15.1151 37.2519 3.13392 12.4337 2.91997 12.1484C2.77734 11.8632 2.77734 11.5779 2.91997 11.5779C3.06261 11.5779 12.5477 10.7221 12.833 10.7221C13.1896 10.7221 13.1182 10.7934 13.2609 11.0074C13.2609 11.0074 26.5971 34.0426 26.811 34.3992C27.0963 34.7558 26.9537 34.8984 26.7397 34.9697ZM55.409 40.3185C55.5516 40.6037 55.7656 40.7464 55.195 40.889C54.6958 41.103 38.0077 46.737 37.6512 46.8796C37.2946 47.0222 37.0806 47.0936 36.6527 46.4517C36.2248 45.8099 30.8048 36.4674 30.8048 36.4674L48.5626 31.8318C48.9905 31.6892 49.1331 31.6179 49.4184 32.0458C49.7037 32.545 55.2664 40.1045 55.409 40.3185ZM56.5501 27.7668C56.1222 27.8381 49.6323 29.4784 49.6323 29.4784L44.2836 22.2041C44.141 21.9901 43.9983 21.7762 44.3549 21.7048C44.7115 21.6335 50.7734 20.5638 51.0587 20.4925C51.3439 20.4211 51.5579 20.3498 51.9145 20.849C52.2711 21.2769 56.8353 27.1249 57.0493 27.3389C57.2632 27.5528 56.978 27.6954 56.5501 27.7668Z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </div>
                </SwiperSlide> <SwiperSlide key={'yii'}>
                <div className="tech-slide yii">
                  <Image
                    src={assets('/images/technologies/yii3-dark.svg')}
                    alt="Yii" className="icon-default"
                    width={60} height={60}
                  />
                  <Image
                    src={assets('/images/technologies/yii3.svg')} alt="Yii"
                    className="icon-hover"
                    width={60} height={60}
                  />
                </div>
              </SwiperSlide>
                <SwiperSlide key={'ruby-on-rails'}>
                  <div className="tech-slide rails">
                    <Image
                      src={assets('/images/technologies/ruby-on-rails-dark.svg')}
                      alt="Ruby On Rails"
                      className="icon-default"
                      width={60} height={60}
                    />
                    <Image
                      width={60} height={60}
                      src={assets('/images/technologies/ruby-on-rails.svg')}
                      alt="Ruby On Rails"
                      className="icon-hover"
                    />
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'wordpress'}>
                  <div className="tech-slide wordpress">
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-default"
                    >
                      <g clipPath="url(#clip0_293_1309)">
                        <path
                          d="M4.26392 29.9925C4.26392 40.1798 10.1839 48.9826 18.7683 53.1548L6.49206 19.5188C5.02041 22.8143 4.26114 26.3834 4.26392 29.9925ZM47.3732 28.6938C47.3732 25.5132 46.2307 23.3106 45.2508 21.5961C43.9463 19.4762 42.724 17.681 42.724 15.5611C42.724 13.1953 44.5177 10.9931 47.0451 10.9931C47.1592 10.9931 47.2674 11.0073 47.3791 11.0137C42.7999 6.81894 36.6997 4.25781 29.9996 4.25781C21.0087 4.25781 13.0986 8.87079 8.49689 15.8578C9.10069 15.8759 9.67021 15.8882 10.1531 15.8882C12.8449 15.8882 17.0118 15.5616 17.0118 15.5616C18.3991 15.4798 18.5627 17.5174 17.1773 17.6815C17.1773 17.6815 15.7826 17.8455 14.2313 17.9268L23.6032 45.803L29.2357 28.9118L25.2255 17.9263C23.8397 17.845 22.5268 17.681 22.5268 17.681C21.14 17.5997 21.3025 15.4793 22.6894 15.5611C22.6894 15.5611 26.9395 15.8877 29.4683 15.8877C32.1597 15.8877 36.327 15.5611 36.327 15.5611C37.7153 15.4793 37.8784 17.5169 36.4921 17.681C36.4921 17.681 35.0949 17.845 33.5465 17.9263L42.8469 45.5914L45.4134 37.0134C46.5265 33.4537 47.3732 30.897 47.3732 28.6938Z"
                          fill="#7780A1"
                        />
                        <path
                          d="M30.4511 32.2547L22.7295 54.6918C25.0902 55.3875 27.5385 55.7407 29.9996 55.7407C32.9965 55.7407 35.8706 55.2221 38.5458 54.2819C38.4741 54.1695 38.4128 54.0509 38.3627 53.9273L30.4511 32.2547ZM52.5816 17.6562C52.6923 18.476 52.755 19.3565 52.755 20.3026C52.755 22.9142 52.2672 25.8499 50.7981 29.5207L42.937 52.2491C50.588 47.7875 55.7343 39.4983 55.7343 30.0035C55.7412 25.6878 54.6566 21.4404 52.5816 17.6562Z"
                          fill="#7780A1"
                        />
                        <path
                          d="M30 0C13.4584 0 0 13.4574 0 29.999C0 46.5426 13.4579 60 30 60C46.5411 60 60.0015 46.543 60.0015 29.999C60.001 13.4574 46.5411 0 30 0ZM30 58.6244C14.217 58.6244 1.37557 45.783 1.37557 29.999C1.37557 14.2165 14.2165 1.37606 30 1.37606C45.7825 1.37606 58.623 14.2165 58.623 29.999C58.623 45.783 45.7821 58.6244 30 58.6244Z"
                          fill="#7780A1"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_293_1309">
                          <rect width="60" height="60" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-hover"
                    >
                      <g clipPath="url(#clip0_435_5297)">
                        <path
                          d="M4.26392 29.9925C4.26392 40.1798 10.1839 48.9826 18.7683 53.1548L6.49206 19.5188C5.02041 22.8143 4.26114 26.3834 4.26392 29.9925ZM47.3732 28.6938C47.3732 25.5132 46.2307 23.3106 45.2508 21.5961C43.9463 19.4762 42.724 17.681 42.724 15.5611C42.724 13.1953 44.5177 10.9931 47.0451 10.9931C47.1592 10.9931 47.2674 11.0073 47.3791 11.0137C42.7999 6.81894 36.6997 4.25781 29.9996 4.25781C21.0087 4.25781 13.0986 8.87079 8.49689 15.8578C9.10069 15.8759 9.67021 15.8882 10.1531 15.8882C12.8449 15.8882 17.0118 15.5616 17.0118 15.5616C18.3991 15.4798 18.5627 17.5174 17.1773 17.6815C17.1773 17.6815 15.7826 17.8455 14.2313 17.9268L23.6032 45.803L29.2357 28.9118L25.2255 17.9263C23.8397 17.845 22.5268 17.681 22.5268 17.681C21.14 17.5997 21.3025 15.4793 22.6894 15.5611C22.6894 15.5611 26.9395 15.8877 29.4683 15.8877C32.1597 15.8877 36.327 15.5611 36.327 15.5611C37.7153 15.4793 37.8784 17.5169 36.4921 17.681C36.4921 17.681 35.0949 17.845 33.5465 17.9263L42.8469 45.5914L45.4134 37.0134C46.5265 33.4537 47.3732 30.897 47.3732 28.6938Z"
                          fill="white"
                        />
                        <path
                          d="M30.4511 32.2547L22.7295 54.6918C25.0902 55.3875 27.5385 55.7407 29.9996 55.7407C32.9965 55.7407 35.8706 55.2221 38.5458 54.2819C38.4741 54.1695 38.4128 54.0509 38.3627 53.9273L30.4511 32.2547ZM52.5816 17.6562C52.6923 18.476 52.755 19.3565 52.755 20.3026C52.755 22.9142 52.2672 25.8499 50.7981 29.5207L42.937 52.2491C50.588 47.7875 55.7343 39.4983 55.7343 30.0035C55.7412 25.6878 54.6566 21.4404 52.5816 17.6562Z"
                          fill="white"
                        />
                        <path
                          d="M30 0C13.4584 0 0 13.4574 0 29.999C0 46.5426 13.4579 60 30 60C46.5411 60 60.0015 46.543 60.0015 29.999C60.001 13.4574 46.5411 0 30 0ZM30 58.6244C14.217 58.6244 1.37557 45.783 1.37557 29.999C1.37557 14.2165 14.2165 1.37606 30 1.37606C45.7825 1.37606 58.623 14.2165 58.623 29.999C58.623 45.783 45.7821 58.6244 30 58.6244Z"
                          fill="white"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_435_5297">
                          <rect width="60" height="60" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'html'}>
                  <div className="tech-slide html">
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-default"
                    >
                      <path
                        d="M8.40169 53.7945L4 0L56.5676 0.108103L51.9133 53.7945L30.4822 60L8.40169 53.7945Z"
                        fill="#7780A1"
                      />
                      <path
                        d="M46.177 17.5355L46.7901 10.9688H13.4889L15.3288 31.1013H38.3114L37.4101 39.688L30.0498 41.6721L22.5814 39.5075L22.1846 34.3842H15.5814L16.4836 44.8473L30.0494 48.6356L43.7233 44.8473L45.5632 24.4265H21.4266L20.7411 17.5355H46.177Z"
                        fill="white"
                      />
                    </svg>
                    <svg
                      width="60"
                      height="60"
                      viewBox="0 0 60 60"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="icon-hover"
                    >
                      <path
                        d="M8.40169 53.7945L4 0L56.5676 0.108103L51.9133 53.7945L30.4822 60L8.40169 53.7945Z"
                        fill="white"
                      />
                      <path
                        d="M46.177 17.5355L46.7901 10.9688H13.4889L15.3288 31.1013H38.3114L37.4101 39.688L30.0498 41.6721L22.5814 39.5075L22.1846 34.3842H15.5814L16.4836 44.8473L30.0494 48.6356L43.7233 44.8473L45.5632 24.4265H21.4266L20.7411 17.5355H46.177Z"
                        fill="#E54C21"
                      />
                    </svg>
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'react'}>
                  <div className="tech-slide react">
                    <Image
                      src={assets('/images/technologies/react.js-dark.svg')}
                      alt="React.js"
                      width={60}
                      height={60}
                      className="icon-default"
                    />
                    <Image
                      src={assets('/images/technologies/react.js.svg')}
                      alt="React.js"
                      width={60}
                      height={60}
                      className="icon-hover"
                    />
                  </div>
                </SwiperSlide>
                <SwiperSlide key={'remix.js'}>
                  <div className="tech-slide remix">
                    <Image
                      src={assets('/images/technologies/remix.js-dark.svg')}
                      alt="Remix.js"
                      width={60}
                      height={60}
                      className="icon-default"
                    />
                    <Image
                      src={assets('/images/technologies/remix.js.svg')}
                      alt="Remix.js"
                      width={60}
                      height={60}
                      className="icon-hover"
                    />
                  </div>
                </SwiperSlide>
              </Swiper>
            </EffectWrapper>
          </div>
        </div>
      </div>
    </>
  );
};

export default Technologies;
