"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { assets } from "@/utils/helper";

const funFactsData = [
  { id: 1, value: "20+", description: "Projects delivered", delay: 100 },
  { id: 2, value: "5+", description: "Years of experience average", delay: 200 },
  { id: 3, value: "100%", description: "Customer satisfaction focus\n", delay: 300 },
  { id: 4, value: "∞", description: "Possibilities to grow with us\n", delay: 400 },
];

const FunFactsArea = () => {
  return (
    <div className="funfacts-area ptb-80 funfacts-cslant-area">
      <div className="container">
        <div
          className="section-title"
          data-aos="fade-up"
          data-aos-delay="100"
          data-aos-duration="700"
          data-aos-once="true"
        >
          <h2>We always try to understand users expectation</h2>
          <div className="bar"></div>
          <p>
            We listen, learn, and adapt to meet user expectations.<br /> Driven
            by feedback and powered by a passionate team. Committed to growth,
            innovation, and long-term partnerships. </p>
        </div>

        <div className="row">
          {funFactsData.map((fact) => (
            <div
              key={fact.id}
              className="col-lg-3 col-md-3 col-6"
              data-aos="fade-up"
              data-aos-delay={fact.delay}
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="funfact">
                <h3>{fact.value}</h3>
                <p>{fact.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div
          className="contact-cta-box"
          data-aos="fade-up"
          data-aos-delay="500"
          data-aos-duration="700"
          data-aos-once="true"
        >
          <h3>Have any question about us?</h3>
          <p>Don&apos;t hesitate to contact us</p>

          <Link href="/contact" className="btn btn-primary"> Contact Us </Link>
        </div>

        <div className="map-bg">
          <Image src={assets('v2/images/map.png')} alt="map" width={910} height={443} />
        </div>
      </div>
      <div className="area-absolute">
        <Image
          src={assets('/images/intro/area-1.svg')}
          alt="shape"
          width={364}
          height={201}
        />
      </div>
    </div>
  );
};

export default FunFactsArea;
