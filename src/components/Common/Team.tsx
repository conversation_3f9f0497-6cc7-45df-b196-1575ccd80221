"use client";

import React, { JSX } from "react";
import * as Icon from "react-feather";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import { assets } from "@/utils/helper";
import { teamData } from "@/api/team";

const data = teamData;

// set icon by type on each object
const socialIcons: Record<string, JSX.Element> = {
  facebook: <Icon.Facebook />,
  twitter: <Icon.Twitter />,
  linkedin: <Icon.Linkedin />,
  youtube: <Icon.Youtube />,
  instagram: <Icon.Instagram />,
  website: <Icon.Globe />,
  github: <Icon.GitHub />,
  email: <Icon.Mail />,
}

const getSocialIcon = (type: string) => {
  return socialIcons[type] || <Icon.Globe />;
};

const Team = () => {
  return (
    <div className="team-area ptb-80 bg-f9f6f6">
      <div className="container">
        <div className="section-title">
          <h2>Our Awesome Team</h2>
          <div className="bar"></div>
          <p>
            Meet the passionate experts behind our success.<br />
            Driven by innovation, collaboration, and excellence.
          </p>
        </div>
      </div>

      <Swiper
        spaceBetween={30}
        pagination={{
          clickable: true,
        }}
        autoplay={{
          delay: 6000,
          pauseOnMouseEnter: true,
        }}
        speed={1200}
        breakpoints={{
          0: {
            slidesPerView: 1,
          },
          576: {
            slidesPerView: 2,
          },
          1024: {
            slidesPerView: 3,
          },
          1200: {
            slidesPerView: 4,
          },
          1500: {
            slidesPerView: 5,
          },
        }}
        modules={[Pagination, Autoplay]}
        className="team-slider"
      >
        {data.map((member, index) => (
          <SwiperSlide key={index}>
            <div className="single-team">
              <div className="team-image team-image-custom">
                <Image
                  title={member.sub_description ?? ""}
                  src={member.image}
                  alt={member.name}
                  width={125}
                  height={125}
                />
              </div>

              <div className="team-content">
                <div className="team-info">
                  <h3>{member.name}</h3>
                  <span>{member.position}</span>
                </div>

                <ul>
                  {Object.entries(member.socialLinks).map(([key, link]) => (
                    <li key={key}>
                      <a
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {getSocialIcon(link.type)}
                      </a>
                    </li>
                  ))}
                </ul>

                <p>{member.description}</p>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Team;
