"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import * as Icon from "react-feather";

// Shape Images
import { assets } from '@/utils/helper';
import { LOGO, MAP, SHAPE_IMAGE, SHAPE_SVG } from '@/constants/asset';
import {
  CSLANT_ADDRESS_SHORT,
  CSLANT_CONTACT_EMAIL,
  CSLANT_PHONE_DISPLAY,
  CSLANT_PHONE_NUMBER,
  SOCIAL_FACEBOOK_URL,
  SOCIAL_GITHUB_URL,
  SOCIAL_GOOGLE_MAP_URL,
  SOCIAL_LINKEDIN_URL,
  SOCIAL_TWITTER_URL,
  SOCIAL_YOUTUBE_URL
} from '@/constants/app';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const companyLinks = [
    { href: "/about", label: "About Us" },
    { href: "/services", label: "Services" },
    { href: "https://docs.cslant.com", label: "Documentation", target: "_blank" },
    { href: "/blog", label: "Latest News" },
  ];

  const supportLinks = [
    { href: "/faq", label: "FAQ's" },
    { href: "/privacy-policy", label: "Privacy Policy" },
    { href: "/cookie-policy", label: "Cookie Policy" },
    { href: "/terms-conditions", label: "Terms & Conditions" },
    { href: "/team", label: "Team" },
    { href: "/contact", label: "Contact Us" },
  ];

  const contactInfo = [
    {
      icon: <Icon.MapPin />,
      link: SOCIAL_GOOGLE_MAP_URL,
      linkText: CSLANT_ADDRESS_SHORT,
      className: "address",
    },
    {
      icon: <Icon.Mail />,
      text: "Email: ",
      link: `mailto:${CSLANT_CONTACT_EMAIL}`,
      linkText: CSLANT_CONTACT_EMAIL,
      className: "email",
    },
    {
      icon: <Icon.PhoneCall />,
      text: "Phone: ",
      link: `tel:${CSLANT_PHONE_NUMBER}`,
      linkText: CSLANT_PHONE_DISPLAY,
      className: "phone",
    },
  ];

  const socialLinks = [
    {
      href: SOCIAL_GITHUB_URL,
      icon: <Icon.GitHub />,
      className: "github",
    },
    {
      href: SOCIAL_FACEBOOK_URL,
      icon: <Icon.Facebook />,
      className: "facebook",
    },
    {
      href: SOCIAL_TWITTER_URL,
      icon: <Icon.Twitter />,
      className: "twitter",
    },
    {
      href: SOCIAL_YOUTUBE_URL,
      icon: <Icon.Youtube />,
      className: "youtube",
    },
    {
      href: SOCIAL_LINKEDIN_URL,
      icon: <Icon.Linkedin />,
      className: "linkedin",
    },
  ];

  return (
    <footer className="footer-area footer-cslant-area bg-f7fafd">
      <div className="container">
        <div className="row">
          {/* Logo and Description */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="100"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget">
              <div className="logo">
                <Link href="/">
                  <Image src={assets(LOGO)} alt="logo" width={110} height={36} />
                </Link>
              </div>
              <p>
                We are a passionate team dedicated to crafting smart, scalable digital solutions that empower businesses to thrive in a fast-changing world. With a focus on innovation, efficiency, and user experience, we turn bold ideas into powerful products that deliver real value.
              </p>
            </div>
          </div>

          {/* Company Links */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="200"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget ps-5">
              <h3 className="single-footer-item-title">Company</h3>
              <ul className="list">
                {companyLinks.map((link, index) => (
                  <li key={index} className="duration-300">
                    <Link href={link.href} {...link.target ? { target: link.target } : {}}>{link.label}</Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Support Links */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="300"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget">
              <h3 className="single-footer-item-title">Support</h3>
              <ul className="list">
                {supportLinks.map((link, index) => (
                  <li key={index} className="duration-300">
                    <Link href={link.href}>{link.label}</Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Contact Info */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="400"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget">
              <h3 className="single-footer-item-title">Address</h3>
              <ul className="footer-contact-info">
                {contactInfo.map((info, index) => (
                  <li key={index}>
                    <p className={`footer-contact-info-item ${info.className}`}>
                      {info.icon} {info.text}
                      {info.link && <a href={info.link} target={'_blank'}>{info.linkText}</a>}
                    </p>
                  </li>
                ))}
              </ul>

              <ul className="social-links">
                {socialLinks.map((social, index) => (
                  <li key={index}>
                    <a
                      href={social.href}
                      className={social.className}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {social.icon}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Copyright */}
          <div className="col-lg-12 col-md-12">
            <div className="copyright-area">
              <p>
                Copyright &copy; {currentYear} by CSlant.
              </p>
            </div>
          </div>
        </div>
      </div>

      <Image src={assets(MAP)} className="map" alt="map" width={910} height={443} />

      {/* Shape Images */}
      <div className="shape1">
        <Image src={assets(SHAPE_IMAGE(1))} alt="shape" width={202} height={202} />
      </div>
      <div className="shape8 rotateme">
        <Image src={assets(SHAPE_SVG(2))} alt="shape" width={22} height={22} />
      </div>
      <div className="area-absolute right-shape">
        <Image src={assets('/images/intro/area-4.svg')} alt="shape" width={55} height={99} />
      </div>
      <div className="area-absolute">
        <Image src={assets('/images/intro/area-2.svg')} alt="shape" width={79} height={94} />
      </div>
    </footer>
  );
};

export default Footer;
