"use client";

import EffectWrapper from '@/components/EffectWrapper';
import { assets, effectVariants } from '@/utils/helper';
import Image from "next/image";

const AboutUsV1Content2 = () => {
  return (
    <section className="about-v1 about-v1-second-section">
      <div className="container">
        <div className="content-wrapper">
          <div className="left-column">
            <EffectWrapper variants={effectVariants(4)} delayTime={0.7}>
              <div className="image-wrapper">
                <Image
                  src={assets('/images/about/about-image-2.svg')}
                  alt="light cslant about"
                  className="image light drop-shadow-three"
                  width={500}
                  height={480}
                />
                <Image
                  src={assets('images/about/about-image-2-dark.svg')}
                  alt="dark cslant about"
                  className="image dark"
                  width={500}
                  height={480}
                />
              </div>
            </EffectWrapper>
          </div>
          <div className="right-column">
            <EffectWrapper variants={effectVariants(1)} delayTime={0.5}>
              <div className="text-wrapper">
                <div className="text-block">
                  <h3 className="title">
                    Comprehensive and Scalable Technology Stack
                  </h3>
                  <p className="paragraph">
                    We leverage a diverse and adaptable tech stack that integrates the best of modern development tools and languages. From PHP (Laravel, Symfony) to JavaScript (React, Vue.js), Ruby on Rails, and more, our approach combines flexibility and power to meet unique project needs.
                  </p>
                </div>
                <div className="text-block">
                  <h3 className="title">
                    Dedicated Customer Support
                  </h3>
                  <p className="paragraph">
                    Our support team is available around the clock to assist you,
                    ensuring you receive the help you need to keep things running
                    smoothly.
                  </p>
                </div>
                <div className="text-block">
                  <h3 className="title">
                    Seamless Performance
                  </h3>
                  <p className="paragraph">
                    We deliver code that’s crafted for stability and efficiency,
                    allowing your business to thrive without technical
                    hiccups.
                  </p>
                </div>
              </div>
            </EffectWrapper>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUsV1Content2;
