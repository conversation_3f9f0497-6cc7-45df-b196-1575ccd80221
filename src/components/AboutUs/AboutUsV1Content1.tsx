"use client";

import EffectWrapper from '@/components/EffectWrapper';
import { assets, effectVariants } from '@/utils/helper';
import SectionTitle from "@/components/Common/SectionTitle";
import Image from "next/image";

const checkIcon = (
  <svg width="16" height="13" viewBox="0 0 16 13" className="fill-current">
    <path d="M5.8535 12.6631C5.65824 12.8584 5.34166 12.8584 5.1464 12.6631L0.678505 8.1952C0.483242 7.99994 0.483242 7.68336 0.678505 7.4881L2.32921 5.83739C2.52467 5.64193 2.84166 5.64216 3.03684 5.83791L5.14622 7.95354C5.34147 8.14936 5.65859 8.14952 5.85403 7.95388L13.3797 0.420561C13.575 0.22513 13.8917 0.225051 14.087 0.420383L15.7381 2.07143C15.9333 2.26669 15.9333 2.58327 15.7381 2.77854L5.8535 12.6631Z" />
  </svg>
);

const AboutUsV1Content1 = () => {
  const List = ({ text }: { text: string }) => (
    <p className="list-item">
      <span className="icon-wrapper rounded-md">{checkIcon}</span>
      {text}
    </p>
  );

  return (
    <section id="about" className="about-v1 about-v1-first-section">
      <div className="container">
        <div className="content-wrapper">
          <div className="left-column">
            <EffectWrapper variants={effectVariants(4)} delayTime={0.1}>
              <SectionTitle
                title="Built to Empower Your Business Growth"
                paragraph="CSlant is more than a platform—it's a supportive community for developers, designers, and creators who believe in sharing knowledge and helping each other grow."
                mb="44px"
              />

              <div className="list-wrapper">
                <div className="list">
                  <div className="list-column">
                    <EffectWrapper variants={effectVariants(4)} delayTime={0.4}>
                      <List text="Detailed documentation" />
                    </EffectWrapper>
                    <EffectWrapper variants={effectVariants(4)} delayTime={0.5}>
                      <List text="Comprehensive business tools" />
                    </EffectWrapper>
                    <EffectWrapper variants={effectVariants(4)} delayTime={0.6}>
                      <List text="Long-term scalability" />
                    </EffectWrapper>
                  </div>

                  <div className="list-column">
                    <EffectWrapper variants={effectVariants(4)} delayTime={0.7}>
                      <List text="Tailored solutions" />
                    </EffectWrapper>
                    <EffectWrapper variants={effectVariants(4)} delayTime={0.8}>
                      <List text="Knowledge-sharing community" />
                    </EffectWrapper>
                    <EffectWrapper variants={effectVariants(4)} delayTime={0.9}>
                      <List text="Developer-friendly design" />
                    </EffectWrapper>
                  </div>
                </div>
              </div>
              <SectionTitle
                title=""
                paragraph="We provide an all-in-one solution tailored for modern businesses looking to build, manage, and scale effectively."
                mb="44px"
              />
            </EffectWrapper>
          </div>
          <div className="right-column">
            <EffectWrapper variants={effectVariants(1)} delayTime={0.3}>
              <div className="image-wrapper">
                <Image
                  src={assets('/images/about/about-image.svg')}
                  alt="CSlant platform preview"
                  className="image light drop-shadow-three"
                  width={464}
                  height={446}
                />
                <Image
                  src={assets('/images/about/about-image-dark.svg')}
                  alt="CSlant platform preview (dark mode)"
                  className="image dark"
                  width={464}
                  height={446}
                />
              </div>
            </EffectWrapper>
          </div>
      </div>
      </div>
    </section>
  );
};

export default AboutUsV1Content1;
