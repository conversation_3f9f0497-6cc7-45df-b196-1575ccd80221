import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { TBlogPost } from '@/types/blog';
import { formatDate } from '@/utils/blog';

interface RelatedPostsProps {
  posts: TBlogPost[];
  currentPostId: number;
}

const RelatedPosts: React.FC<RelatedPostsProps> = ({ posts, currentPostId }) => {
  // Filter out current post
  const relatedPosts = posts.filter(post => post.id !== currentPostId).slice(0, 3);

  if (relatedPosts.length === 0) {
    return null;
  }

  return (
    <div className="related-posts-area">
      <h3>Related Posts</h3>
      <div className="row">
        {relatedPosts.map((post) => (
          <div key={post.id} className="col-lg-4 col-md-6">
            <div className="single-blog-post">
              <div className="blog-image">
                <Link href={`/blog/${post.slug}`}>
                  <Image
                    src={post.image}
                    alt={post.name}
                    width={350}
                    height={200}
                  />
                </Link>
              </div>

              <div className="blog-post-content">
                <h3>
                  <Link href={`/blog/${post.slug}`}>
                    {post.name}
                  </Link>
                </h3>
                
                <div className="blog-post-meta">
                  <ul>
                    <li>
                      <i className="flaticon-user"></i>
                      {post.author?.full_name || 'Anonymous'}
                    </li>
                    <li>
                      <i className="flaticon-calendar"></i>
                      {formatDate(post.created_at)}
                    </li>
                  </ul>
                </div>

                <p>{post.description}</p>

                <Link href={`/blog/${post.slug}`} className="read-more-btn">
                  Read More <i className="flaticon-right"></i>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RelatedPosts;
