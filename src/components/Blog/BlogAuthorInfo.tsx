import React from 'react';
import Image from 'next/image';
import { TBlogPost } from '@/types/blog';
import { calculateReadingTime } from '@/utils/blog';

interface BlogAuthorInfoProps {
  blogPost: TBlogPost;
}

const BlogAuthorInfo: React.FC<BlogAuthorInfoProps> = ({ blogPost }) => {
  const readingTime = calculateReadingTime(blogPost.content);

  return (
    <div className="blog-author-info">
      <div className="author-details">
        {blogPost.author?.image && (
          <div className="author-avatar">
            <Image
              src={blogPost.author.image}
              alt={blogPost.author.full_name}
              width={60}
              height={60}
              className="rounded-circle"
            />
          </div>
        )}
        
        <div className="author-content">
          <h4 className="author-name">{blogPost.author?.full_name || 'Anonymous'}</h4>
          {blogPost.author?.role && (
            <p className="author-role">{blogPost.author.role}</p>
          )}
          <div className="reading-time">
            <span>📖 {readingTime} min read</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogAuthorInfo;
