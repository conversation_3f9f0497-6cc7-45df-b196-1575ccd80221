import React from "react";
import Link from "next/link";
import { CSLANT_CONTACT_EMAIL } from "@/constants/app";

const TermsAndConditionsPage: React.FC = () => {
  return (
    <>
      <div id="termsAndConditions" className="main-text-area ptb-80 landing-page-custom">
        <div className="container">
          <h1>Terms and Conditions for CSlant</h1>
          <p>
            <strong>Effective Date:</strong> 12/28/2024
          </p>

          <p>
            Welcome to CSlant. By accessing or using our website, you agree to comply with and be
            bound by the following terms and conditions. Please read them carefully. If you do not agree
            to these terms, you must not use our website.
          </p>

          <h2>1. Acceptance of Terms</h2>
          <p>
            By accessing or using CSlant, you affirm that you are at least 18 years old or have
            the legal capacity to enter into these terms. If you are using the website on behalf of
            a company or organization, you represent that you have the authority to bind that entity
            to these terms.
          </p>

          <h2>2. Changes to Terms</h2>
          <p>
            We reserve the right to modify these terms at any time. Any changes will be effective
            immediately upon posting. Your continued use of the website following any changes
            constitutes your acceptance of the revised terms.
          </p>

          <h2>3. Use of the Website</h2>
          <p>You agree to use CSlant only for lawful purposes. You must not:</p>
          <ul>
            <li>Use the website in any way that breaches any applicable laws or regulations.</li>
            <li>Engage in any activity that could harm or disrupt the website.</li>
            <li>
              Attempt to gain unauthorized access to any part of the website or its related systems.
            </li>
          </ul>

          <h2>4. Intellectual Property</h2>
          <p>
            All content on CSlant, including text, graphics, logos, and software, is the property
            of CSlant or its licensors and is protected by intellectual property laws. You may
            not reproduce, distribute, or create derivative works from any content without prior
            written consent.
          </p>

          <h2>5. Information Sharing</h2>
          <p>We are committed not to sell, exchange, or rent your personal information to third parties. We only share your personal information in the following cases:</p>
          <ul>
            <li>With service providers we collaborate with to support our operations.</li>
            <li>When we believe that disclosure is necessary to comply with the law, protect our rights, or safeguard the safety of others.</li>
          </ul>

          <h2>6. Information Security</h2>
          <p>
            We use reasonable technical and organizational security measures to protect your personal information from loss, misuse, and unauthorized access. However, no method of transmission over the internet or electronic storage is completely secure, so we cannot guarantee the absolute security of your information.
          </p>

          <h2>7. Your Rights</h2>
          <p>
            You have the right to access, modify, or delete your personal information at any time. If you wish to exercise any of your rights, please contact us using the contact information provided below.
          </p>

          <h2>8. User Accounts</h2>
          <p>
            If you create an account on CSlant, you are responsible for maintaining the
            confidentiality of your account details. You agree to notify us immediately of any
            unauthorized use of your account.
          </p>

          <h2>9. Privacy Policy</h2>
          <p>
            Your use of the website is also governed by our{' '}
            <Link href="/privacy-policy">
              <strong>CSlant Privacy Policy</strong>
            </Link>
            , which explains how we collect, use, and protect your information.
          </p>

          <h2>10. Third-Party Links</h2>
          <p>
            CSlant may contain links to third-party websites. We are not responsible for the
            content, policies, or practices of these third-party websites. Accessing these links is
            at your own risk.
          </p>

          <h2>11. Disclaimers</h2>
          <p>
            CSlant specializes in designing websites and offering package solutions to help
            businesses establish their online presence. The website is provided “AS IS” without any
            warranties, express or implied. CSlant disclaims all warranties, including but not
            limited to merchantability, fitness for a particular purpose, and non-infringement. We do
            not guarantee the accuracy or completeness of any content on the website.
          </p>

          <h2>12. Limitation of Liability</h2>
          <p>
            To the fullest extent permitted by law, CSlant shall not be liable for any indirect,
            incidental, consequential, or punitive damages arising from your use of the website.
          </p>

          <h2>13. Indemnification</h2>
          <p>
            You agree to indemnify and hold CSlant harmless from any claims, damages, or expenses
            arising out of your use of the website or violation of these terms.
          </p>

          <h2>14. Termination</h2>
          <p>
            We reserve the right to terminate or suspend your access to the website without notice
            for any violation of these terms.
          </p>

          <h2>15. Contact Information</h2>
          <p>If you have any questions about these terms, please contact us at:</p>
          <p>
            <strong>Email:</strong>{' '}
            <a href={`mailto:${CSLANT_CONTACT_EMAIL}`} className="text-primary">
              {CSLANT_CONTACT_EMAIL}
            </a>
          </p>
        </div>
      </div>
    </>
  );
};

export default TermsAndConditionsPage;
