"use client";

import React from "react";
import * as Icon from "react-feather";
import Link from "next/link";

const featuresData = [
  {
    id: 1,
    title: "Tailored Development",
    description:
      "Bespoke web and mobile solutions crafted to fit your business needs and accelerate growth.",
    icon: <Icon.Server />,
    link: "#",
    delay: 100,
    background: "",
  },
  {
    id: 2,
    title: "Secure Architecture",
    description:
      "Build systems with a strong security foundation, ensuring performance, scalability, and trust.",
    icon: <Icon.Code />,
    link: "#",
    delay: 200,
    background: "bg-f78acb",
  },
  {
    id: 3,
    title: "Agile Collaboration",
    description:
      "Work closely with our team using agile methods for faster, more flexible project delivery.",
    icon: <Icon.Users />,
    link: "#",
    delay: 300,
    background: "bg-c679e3",
  },
  {
    id: 4,
    title: "Future-Ready Integration",
    description:
      "Connect new technologies, AI, cloud services, and APIs into your business flow.",
    icon: <Icon.GitBranch />,
    link: "#",
    delay: 400,
    background: "bg-eb6b3d",
  },
];

const Features: React.FC = () => {
  return (
    <>
      <div className="boxes-area">
        <div className="container">
          <div className="row justify-content-center">
            {featuresData.slice(0, 4).map((feature) => (
              <div
                key={feature.id}
                className={`col-lg-3 col-md-6 ${feature.background}`}
                data-aos="fade-up"
                data-aos-delay={feature.delay}
                data-aos-duration="700"
                data-aos-once="true"
              >
                <div className={`single-box ${feature.background}`}>
                  <div className="icon">{feature.icon}</div>

                  <h3>
                    <Link href={feature.link}>{feature.title}</Link>
                  </h3>

                  <p>{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default Features;
