"use client";

import React from "react";
import * as Icon from "react-feather";
import Image from "next/image";
import { assets } from "@/utils/helper";

const servicesData = [
  { id: 1, icon: <Icon.Slack />, title: "Requirements Gathering" },
  { id: 2, icon: <Icon.Table />, title: "Planning & Estimation" },
  { id: 3, icon: <Icon.Figma />, title: "UI/UX Prototyping" },
  { id: 4, icon: <Icon.Codepen />, title: "Backend/API Design" },
  { id: 5, icon: <Icon.Code />, title: "Frontend Development" },
  { id: 6, icon: <Icon.RefreshCcw />, title: "Testing & QA" },
  { id: 7, icon: <Icon.Cloud />, title: "Deployment" },
  { id: 8, icon: <Icon.Package />, title: "Post-launch Support" },
];

const imagesData = [
  {
    id: 1,
    src: assets('/v2/images/services-right-image/book-self.png'),
    alt: "book-self",
    delay: 100,
    width: 139,
    height: 139,
  },
  {
    id: 2,
    src: assets('/v2/images/services-right-image/box.png'),
    alt: "box",
    delay: 150,
    width: 420,
    height: 251,
  },
  {
    id: 3,
    src: assets('/v2/images/services-right-image/chair.png'),
    alt: "chair",
    delay: 200,
    width: 67,
    height: 127,
  },
  {
    id: 4,
    src: assets('/v2/images/services-right-image/cloud.png'),
    alt: "cloud",
    delay: 250,
    width: 158,
    height: 140,
  },
  {
    id: 5,
    src: assets('/v2/images/services-right-image/cup.png'),
    alt: "cup",
    delay: 300,
    width: 82,
    height: 93,
  },
  {
    id: 6,
    src: assets('/v2/images/services-right-image/flower-top.png'),
    alt: "flower",
    delay: 350,
    width: 27,
    height: 78,
  },
  {
    id: 7,
    src: assets('/v2/images/services-right-image/head-phone.png'),
    alt: "head-phone",
    delay: 400,
    width: 30,
    height: 29,
  },
  {
    id: 8,
    src: assets('/v2/images/services-right-image/monitor.png'),
    alt: "monitor",
    delay: 450,
    width: 70,
    height: 99,
  },
  {
    id: 9,
    src: assets('/v2/images/services-right-image/mug.png'),
    alt: "mug",
    delay: 500,
    width: 16,
    height: 20,
  },
  {
    id: 10,
    src: assets('/v2/images/services-right-image/table.png'),
    alt: "table",
    delay: 550,
    width: 162,
    height: 149,
  },
  {
    id: 11,
    src: assets('/v2/images/services-right-image/tissue.png'),
    alt: "tissue",
    delay: 600,
    width: 26,
    height: 42,
  },
  {
    id: 12,
    src: assets('/v2/images/services-right-image/water-bottle.png'),
    alt: "water-bottle",
    delay: 650,
    width: 14,
    height: 37,
  },
  {
    id: 13,
    src: assets('/v2/images/services-right-image/wifi.png'),
    alt: "wifi",
    delay: 700,
    width: 55,
    height: 71,
  },
  {
    id: 14,
    src: assets('/v2/images/services-right-image/cercle-shape.png'),
    alt: "shape",
    delay: 750,
    width: 524,
    height: 518,
    className: "bg-image rotateme",
  },
  {
    id: 15,
    src: assets('/v2/images/services-right-image/service-right-main-pic.png'),
    alt: "main-pic",
    delay: 800,
    width: 342,
    height: 396,
  },
];

const CloudHostingServices: React.FC = () => {
  return (
    <div className="services-area ptb-80 bg-f7fafd">
      <div className="container">
        <div className="row justify-content-center align-items-center">
          <div className="col-lg-6 col-md-12 services-content">
            <div className="section-title">
              <h2>Our Development Workflow</h2>
              <div className="bar"></div>
              <p>
                A collaborative, agile-driven process to deliver high-quality, scalable digital products.
              </p>
            </div>

            <div className="row">
              {servicesData.map((service) => (
                <div key={service.id} className="col-lg-6 col-md-6">
                  <div className="box">
                    {service.icon} {service.title}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="col-lg-6 col-md-12 services-right-image">
            {imagesData.map((image) => (
              <Image
                key={image.id}
                src={image.src}
                alt={image.alt}
                data-aos="fade-up"
                data-aos-delay={image.delay}
                data-aos-duration="700"
                data-aos-once="true"
                width={image.width}
                height={image.height}
                className={image.className || ""}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CloudHostingServices;
