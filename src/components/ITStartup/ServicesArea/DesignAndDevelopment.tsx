"use client";

import React from "react";
import * as Icon from "react-feather";
import Image from "next/image";

const imagesData = [
  {
    id: 1,
    src: "/images/services-left-image/big-monitor.png",
    alt: "big-monitor",
    delay: 100,
    width: 252,
    height: 149,
  },
  {
    id: 2,
    src: "/images/services-left-image/creative.png",
    alt: "creative",
    delay: 150,
    width: 88,
    height: 94,
  },
  {
    id: 3,
    src: "/images/services-left-image/developer.png",
    alt: "developer",
    delay: 200,
    width: 149,
    height: 230,
  },
  {
    id: 4,
    src: "/images/services-left-image/flower-top.png",
    alt: "flower-top",
    delay: 250,
    width: 127,
    height: 203,
  },
  {
    id: 5,
    src: "/images/services-left-image/small-monitor.png",
    alt: "small-monitor",
    delay: 300,
    width: 109,
    height: 106,
  },
  {
    id: 6,
    src: "/images/services-left-image/small-top.png",
    alt: "small-top",
    delay: 350,
    width: 56,
    height: 82,
  },
  {
    id: 7,
    src: "/images/services-left-image/table.png",
    alt: "table",
    delay: 400,
    width: 337,
    height: 138,
  },
  {
    id: 8,
    src: "/images/services-left-image/target.png",
    alt: "target",
    delay: 450,
    width: 91,
    height: 96,
  },
  {
    id: 9,
    src: "/images/services-left-image/cercle-shape.png",
    alt: "shape",
    delay: 500,
    width: 524,
    height: 518,
    className: "bg-image rotateme",
  },
  {
    id: 10,
    src: "/images/services-left-image/service-left-main-pic.png",
    alt: "main-pic",
    delay: 550,
    width: 418,
    height: 378,
  },
];

const servicesData = [
  { id: 1, icon: <Icon.Layout />, title: "Responsive design and development" },
  { id: 2, icon: <Icon.Code />, title: "Full-stack web development (React, Next.js, Laravel)" },
  { id: 3, icon: <Icon.ShoppingCart />, title: "E-commerce, CMS, and SaaS development" },
  { id: 4, icon: <Icon.Code />, title: "Backend development (Node.js, NestJS, PHP)" },
  { id: 5, icon: <Icon.Smartphone />, title: "iOS apps development" },
  { id: 6, icon: <Icon.Percent />, title: "UX/UI Design" },
  { id: 7, icon: <Icon.Smartphone />, title: "Android apps development" },
  { id: 8, icon: <Icon.CheckCircle />, title: "Print ready design" },
];

const DesignAndDevelopment: React.FC = () => {
  return (
    <div className="services-area ptb-80">
      <div className="container">
        <div className="row h-100 justify-content-center align-items-center">
          <div className="col-lg-6 col-md-12 services-left-image">
            {imagesData.map((image) => (
              <Image
                key={image.id}
                src={image.src}
                alt={image.alt}
                data-aos="fade-up"
                data-aos-delay={image.delay}
                data-aos-duration="700"
                data-aos-once="true"
                width={image.width}
                height={image.height}
                className={image.className || ""}
              />
            ))}
          </div>

          <div className="col-lg-6 col-md-12 services-content">
            <div className="container">

              <div className="section-title">
                <h2>Design & Development</h2>
                <div className="bar"></div>
                <p>
                  We bring expertise in frontend frameworks (Next.js, React, Vue.js) and backend technologies (Laravel, Node.js, NestJS, PHP, RoR), offering custom, high-quality software development tailored for startups, enterprises, and growing businesses.
                </p>
              </div>

              <div className="row">
                {servicesData.map((service) => (
                  <div key={service.id} className="col-lg-6 col-md-6">
                    <div className="box">
                      {service.icon} {service.title}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesignAndDevelopment;
