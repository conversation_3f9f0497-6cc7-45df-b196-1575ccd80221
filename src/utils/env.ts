// ################ App environment #######################
export const APP_ENV = process.env.NEXT_PUBLIC_APP_ENV;
export const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME;
export const APP_URL = process.env.NEXT_PUBLIC_APP_URL;

export const HTTP_PORT = APP_ENV !== 'local' ? '' : `:${process.env.NEXT_PUBLIC_HTTP_PORT}`;
export const HTTPS_PORT = APP_ENV !== 'local' ? '' : `:${process.env.NEXT_PUBLIC_HTTPS_PORT}`;

export const IS_USE_HTTPS = process.env.NEXT_PUBLIC_IS_USE_HTTPS === 'true';
export const HTTP_PROTOCOL = IS_USE_HTTPS ? 'https://' : 'http://';
export const APP_PORT = IS_USE_HTTPS ? HTTPS_PORT : HTTP_PORT;

export const FRONT_END_DOMAIN = `${HTTP_PROTOCOL}${process.env.NEXT_PUBLIC_BASE_DOMAIN}${APP_PORT}`;

// ################# Assets config #####################
export const ASSETS_DOMAIN = process.env.NEXT_PUBLIC_ASSETS_DOMAIN;
export const ASSETS_URL = `${HTTP_PROTOCOL}${ASSETS_DOMAIN}${APP_PORT}`;

export const BLOG_ASSETS_PATH = process.env.NEXT_PUBLIC_BLOG_ASSETS_PATH || '/blog';
export const BLOG_ASSETS_URL = `${ASSETS_URL}${BLOG_ASSETS_PATH}`;

// ################# Cloudflare config #####################
export const CLOUDFLARE_ANALYTICS_TOKEN = process.env.NEXT_PUBLIC_CLOUDFLARE_ANALYTICS_TOKEN;

// ################# API config #####################
export const MAIN_API_URL = `${APP_ENV !== 'local' ? HTTP_PROTOCOL : 'http://'}${process.env.NEXT_PUBLIC_MAIN_API_URL}`;
export const BLOG_API_URL = `${APP_ENV !== 'local' ? HTTP_PROTOCOL : 'http://'}${process.env.NEXT_PUBLIC_BLOG_API_URL}`;

// ################# Route config #####################
export const ROUTE_API_HANDLER = process.env.NEXT_PUBLIC_ROUTE_API_HANDLER;
