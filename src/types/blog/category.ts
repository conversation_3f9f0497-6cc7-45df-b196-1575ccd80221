import { IBlogPost } from '@/types/blog';
import { IMetaSEO } from "@/types/app";

export type TCategoryResponse = {
  data: TCategoryData,
  error: boolean,
  message: string;
}

export type TCategoryData = {
  id: number,
  name: string;
  slug: string;
  icon: string;
  description: string;
  children: [],
  parent: TParentCate
}
type TParentCate = {
  id: string;
  name: string;
  slug: string;
  url: string;
  description: string;
}
export type TCategoryLoader = {
  category: TCategoryResponse,
  posts: IBlogPost,
  meta?: IMetaSEO,
}
