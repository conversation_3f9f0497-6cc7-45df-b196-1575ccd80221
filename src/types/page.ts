import { JSX, ReactNode } from "react";

// Interface in page for Language
export type Lang = "vi" | "en";

export type LocaleType = "vi_VN" | "en_US";

// Interface in page for submenu children header
export interface SubMenu {
  title?: string;
  url?: string;
}

// Interface in page for submenu header
export interface PropsMenu {
  sub?: Array<SubMenu>;
  title?: string;
  type?: string;
  url?: string;
  length?: number;
  map(arg0: (value: PropsMenu, index: number) => JSX.Element | null): ReactNode;
}
