import { createLogger, format, transports } from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';

const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const logLevel = process.env.LOG_LEVEL || 'info';

const loggerTransports = [];

  loggerTransports.push(
    new transports.Console({
      level: logLevel,
      format: format.combine(
        format.colorize(),
        format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        format.printf(({ timestamp, level, message }) => {
          return `[${timestamp}] ${level}: ${message}`;
        })
      ),
    })
  );

// ✅ Rotate file logs: 5MB per file, max 5 files, keep alive maximum 30 days
loggerTransports.push(
  new DailyRotateFile({
    level: logLevel,
    dirname: logDir,
    filename: 'app-%DATE%.log',
    datePattern: 'YYYY-MM-DD',
    maxSize: '5m',
    maxFiles: '5',
    zippedArchive: false,
    format: format.combine(
      format.uncolorize(),
      format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      format.printf(({ timestamp, level, message }) => {
        return `[${timestamp}] ${level.toUpperCase()}: ${message}`;
      })
    ),
  })
);

const logger = createLogger({
  level: logLevel,
  transports: loggerTransports,
});

logger.info(`🟢 Logger initialized. Level: ${logLevel.toUpperCase()}`);

export default logger;
