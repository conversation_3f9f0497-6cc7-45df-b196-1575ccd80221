
// ==========================
// version with pure response
//===========================
// import logger from './logger';
//
// type Handler = (req: Request) => Promise<Response>;
//
// export function withLogger(handler: Handler): Handler {
//   return async function (req: Request) {
//     const method = req.method;
//     const url = req.url;
//     const start = Date.now();
//     const ip = req.headers.get('x-forwarded-for') || 'Unknown IP';
//
//     let body = '';
//
//     try {
//       if (method !== 'GET' && method !== 'HEAD') {
//         const clone = req.clone();
//         const json = await clone.json();
//         body = JSON.stringify(json);
//       }
//     } catch {
//       body = '[unreadable body]';
//     }
//
//     // Log request
//     logger.info(`Request 📥 [${method}] ${url} from ${ip} | Body: ${body || '[empty]'}`);
//
//     const response = await handler(req);
//
//     const duration = Date.now() - start;
//
//     // Log response
//     logger.info(`Response 📤 [${method}] ${url} -> ${response.status} | Took ${duration}ms`);
//
//     return response;
//   };
// }




import { NextRequest, NextResponse } from 'next/server';
import logger from './logger';

type Handler = (req: NextRequest) => Promise<NextResponse>;

export function withLogger(handler: Handler): Handler {
  return async function (req: NextRequest) {
    const method = req.method;
    const url = req.nextUrl.pathname + '?' + req.nextUrl.searchParams;
    const start = Date.now();
    const ip =
      req.headers.get('x-forwarded-for') ||
      'Unknown IP';

    let body = '';

    try {
      if (method !== 'GET') {
        const clone = req.clone();
        const json = await clone.json();
        body = JSON.stringify(json);
      }
    } catch {
      body = '[unreadable body]';
    }
    // request from user
    logger.info(`Request 📥 [${method}] ${url} from ${ip} | Body: ${body || '[empty]'}`);

    const response = await handler(req);

    const duration = Date.now() - start;
    // response to user
    logger.info(`Response 📤 [${method}] ${url} -> ${response.status} | Took ${duration}ms`);

    return response;
  };
}
