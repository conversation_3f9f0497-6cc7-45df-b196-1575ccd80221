# Blog API Setup - Hướng dẫn sử dụng

## Tổng quan

Đã tạo xong hệ thống API và component để hiển thị blog detail từ database. Hệ thống chỉ lấy data từ API thật, không có demo data hay fallback.

## Cấu trúc Files đã tạo/cập nhật

### 1. Services & API
- `src/services/apis/blog.ts` - Service chính để gọi API blog
- `src/api/blog/post.ts` - API wrapper với error handling
- `src/constants/endpoint.ts` - API endpoints constants

### 2. Components
- `src/components/Blog/BlogDetailsContent.tsx` - Component chính (đã cập nhật)
- `src/components/Blog/BlogAuthorInfo.tsx` - Component hiển thị thông tin tác giả
- `src/components/Blog/RelatedPosts.tsx` - Component hiển thị bài viết liên quan

### 3. Pages & Routing
- `src/app/blog/[slug]/page.tsx` - Dynamic route cho blog detail
- `src/app/blog/[slug]/not-found.tsx` - Error page khi không tìm thấy blog

### 4. Types & Utils
- `src/types/app.ts` - Cập nhật IMetaSEO interface
- `src/types/blog/index.ts` - Types cho blog (đã có sẵn)
- `src/utils/blog.ts` - Utility functions (formatDate, calculateReadingTime, etc.)

## Cách sử dụng

### 1. Truy cập blog detail
```
/blog/[slug]
```
Ví dụ: `/blog/future-web-development-trends-2024`

### 2. API Service Usage
```typescript
import { BlogService } from '@/services/apis/blog';

// Lấy chi tiết blog post
const response = await BlogService.getPostBySlug('your-blog-slug');

// Lấy bài viết liên quan
const relatedPosts = await BlogService.getRelatedPosts(postId, 3);

// Tạo meta SEO
const metaSEO = BlogService.generateMetaSEO(blogPost);
```

### 3. Component Usage
```tsx
<BlogDetailsContent 
  blogPost={blogPost}
  meta={metaSEO}
  relatedPosts={relatedPosts}
/>
```

## Features đã implement

### ✅ Đã hoàn thành
1. **Dynamic routing** với slug
2. **API integration** với fallback demo data
3. **SEO optimization** với dynamic meta tags
4. **Content từ database** thay vì static content
5. **Author information** display
6. **Reading time calculation**
7. **Related posts** section
8. **Categories và tags** linking
9. **Responsive design** (giữ nguyên CSS)
10. **Error handling** với fallback data

### 🔄 Có thể mở rộng sau
1. **Comments system** (đã chuẩn bị structure)
2. **Post navigation** (prev/next posts)
3. **Social sharing** buttons
4. **Search functionality**
5. **Category/tag filtering pages**

## Error Handling

Hệ thống xử lý lỗi khi API không khả dụng:
- Hiển thị 404 page khi không tìm thấy blog post
- Log chi tiết lỗi API để debug
- Graceful handling cho related posts (không bắt buộc)

## API Endpoints cần implement

Backend cần implement các endpoints sau:

```
GET /api/posts/{slug} - Lấy chi tiết blog post
GET /api/posts?related_to={id}&limit={number} - Lấy bài viết liên quan
```

## Response Format

```typescript
// Blog Post Detail
{
  data: {
    id: number,
    name: string,
    slug: string,
    description: string,
    image: string,
    content: string, // HTML content
    categories: Array<{id, name, slug, description}>,
    tags: Array<{id, name, slug, description}>,
    author: {
      first_name: string,
      last_name: string,
      full_name: string,
      image: string,
      role: string
    },
    created_at: string,
    updated_at: string
  },
  meta: {...}
}
```

## Testing

1. **Với real API**: Đảm bảo backend API đang chạy và trả về đúng format
2. **Error handling**: API lỗi sẽ hiển thị 404 page
3. **Debug**: Check console logs để xem chi tiết API calls

## Next Steps

1. **Đảm bảo backend API** - Implement các endpoints cần thiết
2. **Add comments system** - Nếu cần
3. **Implement search** - Tìm kiếm blog posts
4. **Add pagination** - Cho related posts
5. **Social sharing** - Facebook, Twitter, LinkedIn

## Notes

- Hệ thống chỉ lấy data từ database thông qua API
- Component giữ nguyên design và CSS hiện tại
- Tương thích với cấu trúc project hiện tại
- Error handling với 404 page
- SEO friendly với dynamic meta tags
- Mobile responsive
- Có logging để debug API issues
