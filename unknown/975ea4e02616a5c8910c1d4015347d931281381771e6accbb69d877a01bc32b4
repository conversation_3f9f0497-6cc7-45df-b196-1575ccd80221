import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "../../../components/Layout/Navbar";
import Team from "../../../components/Common/Team";
import FunFactsArea from "../../../components/Common/FunFactsArea";
import Partner from "../../../components/Common/Partner";
import Footer from "../../../components/Layout/Footer";
import PageBanner from "../../../components/Common/PageBanner";
import AboutUsContent1 from "../../../components/AboutUs/AboutUsContent1";
import AboutUsV1Content1 from "@/components/AboutUs/AboutUsV1Content1";
import AboutUsV1Content2 from "@/components/AboutUs/AboutUsV1Content2";
import Technologies from "@/components/Common/Technologies";

export default function Page() {
  const t = useTranslations('pages.about');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} headingNumber={1} />

      <AboutUsContent1 />

      <AboutUsV1Content1 />

      <AboutUsV1Content2 />

      <Team />

      {/*<Partner />*/}
      <Technologies />

      <FunFactsArea />

      <Footer />
    </>
  );
}
